/*
 * إصلاح خاص لمشكلة الحجم الكبير على الهواتف
 * Mobile Size Fix - Forces proper mobile display
 */

/* إجبار الصفحة على الحجم الصحيح */
html {
    width: 100vw !important;
    max-width: 100vw !important;
    overflow-x: hidden !important;
    -webkit-text-size-adjust: none !important;
    -ms-text-size-adjust: none !important;
    text-size-adjust: none !important;
}

body {
    width: 100vw !important;
    max-width: 100vw !important;
    min-width: 100vw !important;
    overflow-x: hidden !important;
    margin: 0 !important;
    padding: 0 !important;
    box-sizing: border-box !important;
}

/* إجبار جميع الحاويات على الحجم الصحيح */
* {
    max-width: 100vw !important;
    box-sizing: border-box !important;
}

.container,
.container.mx-auto,
#main-content,
.main-content {
    width: 100% !important;
    max-width: 1200px !important;
    min-width: 320px !important;
    padding-left: 15px !important;
    padding-right: 15px !important;
    margin-left: auto !important;
    margin-right: auto !important;
    overflow-x: hidden !important;
    box-sizing: border-box !important;
}

/* إصلاح الصور */
img,
#main-mod-image,
.main-mod-image {
    width: 100% !important;
    max-width: 100% !important;
    height: auto !important;
    object-fit: cover !important;
    display: block !important;
}

/* إزالة جميع الخلفيات الملونة */
.main-image-glow,
.mod-container,
.thumbnail-container,
.thumbnail-container-centered {
    background: transparent !important;
    background-color: transparent !important;
}

/* إصلاح الشبكة للهواتف */
.grid,
.grid-cols-2 {
    width: 100% !important;
    max-width: 100% !important;
    display: grid !important;
    box-sizing: border-box !important;
}

/* إصلاح خاص للهواتف */
@media screen and (max-width: 768px) {
    html {
        font-size: 14px !important;
        zoom: 1 !important;
        -webkit-text-size-adjust: 100% !important;
    }

    body {
        font-size: 14px !important;
        zoom: 1 !important;
        transform: scale(1) !important;
        -webkit-transform: scale(1) !important;
    }

    .container {
        padding: 10px !important;
        margin: 0 auto !important;
        width: 100% !important;
        max-width: 100% !important;
    }

    .grid-cols-2 {
        grid-template-columns: 1fr 1fr !important;
        gap: 10px !important;
    }

    .grid-cols-2 > div {
        width: 100% !important;
        min-width: 0 !important;
        padding: 12px !important;
    }
    
    /* إصلاح الصورة الرئيسية */
    .main-image-section {
        width: 100% !important;
        margin: 10px 0 !important;
        padding: 0 !important;
    }
    
    .main-image-section .relative {
        width: 100% !important;
        padding-top: 56.25% !important;
    }
    
    /* إصلاح الأزرار */
    .pixel-button,
    .download-button {
        width: 90% !important;
        max-width: 320px !important;
        padding: 18px 12px !important;
        font-size: 14px !important;
        margin: 10px auto !important;
        min-height: 50px !important;
    }

    /* إصلاح شريط التقدم */
    .progress-bar {
        position: absolute !important;
        bottom: 2px !important;
        right: 8px !important;
        height: 2px !important;
        max-width: calc(100% - 16px) !important;
        border-radius: 2px !important;
    }
    
    /* إصلاح النصوص */
    h1, .mod-title {
        font-size: 18px !important;
        line-height: 1.3 !important;
        margin: 10px 0 !important;
    }
    
    .info-label {
        font-size: 12px !important;
    }
    
    .mod-info {
        font-size: 14px !important;
    }
    
    /* إصلاح الهيدر */
    header {
        padding: 10px !important;
        width: 100vw !important;
    }
    
    .header-content {
        width: 100% !important;
        padding: 0 5px !important;
    }
}

/* إصلاح للشاشات الصغيرة جداً */
@media screen and (max-width: 480px) {
    html {
        font-size: 12px !important;
    }
    
    body {
        font-size: 12px !important;
    }
    
    .container {
        padding: 3px !important;
    }
    
    .grid-cols-2 {
        grid-template-columns: 1fr !important;
        gap: 3px !important;
    }
    
    .grid-cols-2 > div {
        padding: 6px !important;
    }
    
    h1, .mod-title {
        font-size: 16px !important;
    }
    
    .pixel-button {
        padding: 16px 10px !important;
        font-size: 12px !important;
        min-height: 48px !important;
        width: 95% !important;
        max-width: 280px !important;
    }
}

/* منع التكبير والتصغير */
@media screen and (max-width: 768px) {
    input, select, textarea, button {
        font-size: 16px !important;
        -webkit-appearance: none !important;
        border-radius: 0 !important;
    }
}

/* إصلاح مشكلة الاتجاه */
@media screen and (max-width: 768px) and (orientation: landscape) {
    .container {
        padding: 5px 10px !important;
    }
    
    .grid-cols-2 {
        grid-template-columns: 1fr 1fr !important;
    }
}

/* إصلاح خاص لـ iOS */
@supports (-webkit-touch-callout: none) {
    html {
        -webkit-text-size-adjust: 100% !important;
        -webkit-font-smoothing: antialiased !important;
    }
    
    body {
        -webkit-overflow-scrolling: touch !important;
        -webkit-transform: translateZ(0) !important;
    }
}

/* إصلاح خاص لـ Android */
@media screen and (-webkit-min-device-pixel-ratio: 1) {
    html {
        -webkit-text-size-adjust: 100% !important;
    }
    
    body {
        -webkit-text-size-adjust: 100% !important;
    }
}

/* إجبار عدم وجود تمرير أفقي */
html, body, .container, #main-content, .main-content {
    overflow-x: hidden !important;
    max-width: 100vw !important;
}

/* إخفاء أي عناصر تسبب التمرير الأفقي */
.main-image-glow .particle {
    display: none !important;
}

/* إصلاح الصور المصغرة */
.thumbnail-container,
.thumbnail-container-centered {
    width: 100% !important;
    max-width: 100% !important;
    overflow-x: auto !important;
    background: transparent !important;
}

.thumbnail {
    background: transparent !important;
    border: 1px solid #666 !important;
}

/* إصلاح الأزرار الثابتة */
.fixed.bottom-0 {
    width: 100vw !important;
    left: 0 !important;
    right: 0 !important;
    padding: 10px !important;
    box-sizing: border-box !important;
}

/* إصلاح نهائي للحجم */
@media screen and (max-width: 768px) {
    * {
        max-width: 100vw !important;
        word-wrap: break-word !important;
        overflow-wrap: break-word !important;
    }
    
    .container * {
        max-width: 100% !important;
    }
}
