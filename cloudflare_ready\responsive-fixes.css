/*
 * إصلاحات التجاوب الإضافية
 * Additional Responsive Fixes
 * هذا الملف يحل مشاكل التجاوب الشائعة على الاستضافة
 */

/* إصلاحات أساسية للتجاوب */
html {
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    text-size-adjust: 100%;
    width: 100%;
    overflow-x: hidden;
}

body {
    width: 100%;
    min-width: 320px;
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch;
}

/* إصلاح مشاكل الفيض الأفقي */
* {
    max-width: 100%;
    box-sizing: border-box;
}

/* إصلاحات للحاويات */
.container,
.container.mx-auto {
    width: 100% !important;
    max-width: 100% !important;
    padding-left: 1rem !important;
    padding-right: 1rem !important;
    margin-left: auto !important;
    margin-right: auto !important;
    box-sizing: border-box !important;
}

/* إصلاحات للشبكة */
.grid {
    width: 100% !important;
    box-sizing: border-box !important;
}

.grid > * {
    min-width: 0 !important;
    box-sizing: border-box !important;
}

/* إصلاحات للصور */
img,
video {
    max-width: 100% !important;
    height: auto !important;
    display: block !important;
}

/* إصلاحات للنصوص */
h1, h2, h3, h4, h5, h6,
p, span, div {
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    max-width: 100% !important;
}

/* إصلاحات للأزرار */
button,
.pixel-button,
.download-button {
    max-width: 100% !important;
    box-sizing: border-box !important;
}

/* إصلاحات خاصة للهواتف */
@media screen and (max-width: 768px) {
    html {
        font-size: 14px;
    }
    
    body {
        font-size: 14px;
        line-height: 1.5;
    }
    
    .container {
        padding: 0.5rem !important;
        margin: 0 !important;
        width: 100% !important;
    }
    
    /* إصلاح الشبكة للهواتف - الحفاظ على التخطيط الأفقي */
    .grid-cols-2 {
        grid-template-columns: 1fr 1fr !important;
        gap: 0.75rem !important;
        display: grid !important;
    }

    .grid-cols-2 > div {
        width: 100% !important;
        min-width: 0 !important;
        box-sizing: border-box !important;
    }
    
    /* إصلاح الصورة الرئيسية */
    .main-image-section {
        width: 100% !important;
        margin-bottom: 1rem !important;
    }
    
    .main-image-section .relative {
        width: 100% !important;
    }
    
    /* إصلاح الأزرار */
    .fixed.bottom-0 {
        padding: 0.5rem !important;
        left: 0 !important;
        right: 0 !important;
        width: 100% !important;
    }
    
    .pixel-button {
        width: 85% !important;
        max-width: 300px !important;
        padding: 18px 10px !important;
        font-size: 1rem !important;
        min-height: 50px !important;
    }
}

/* إصلاحات للشاشات الصغيرة جداً */
@media screen and (max-width: 480px) {
    html {
        font-size: 12px;
    }
    
    .container {
        padding: 0.25rem !important;
    }
    
    /* الحفاظ على التخطيط الأفقي حتى للشاشات الصغيرة جداً */
    .grid-cols-2 {
        grid-template-columns: 1fr 1fr !important;
        gap: 0.5rem !important;
        display: grid !important;
    }

    /* تصغير النصوص بدلاً من تغيير التخطيط */
    .grid-cols-2 .info-label {
        font-size: 10px !important;
    }

    .grid-cols-2 .mod-info {
        font-size: 12px !important;
    }
    
    /* تصغير النصوص */
    .mod-title,
    h1 {
        font-size: 1.2rem !important;
        line-height: 1.3 !important;
    }
    
    .info-label {
        font-size: 0.75rem !important;
    }
    
    .mod-info {
        font-size: 1rem !important;
    }
    
    /* تحسين الأزرار */
    .pixel-button {
        padding: 16px 8px !important;
        font-size: 0.9rem !important;
        min-height: 48px !important;
        width: 90% !important;
        max-width: 260px !important;
    }
}

/* إصلاحات للشاشات العريضة */
@media screen and (min-width: 769px) {
    .container {
        max-width: 1200px !important;
        padding: 2rem !important;
    }
}

/* إصلاحات للطباعة */
@media print {
    * {
        max-width: none !important;
    }
    
    .container {
        width: 100% !important;
        max-width: none !important;
        padding: 0 !important;
    }
}

/* إصلاحات للوضع الأفقي على الهواتف */
@media screen and (max-width: 768px) and (orientation: landscape) {
    .container {
        padding: 0.5rem 1rem !important;
    }
    
    .grid-cols-2 {
        grid-template-columns: 1fr 1fr !important;
    }
}

/* إصلاحات لمتصفحات iOS */
@supports (-webkit-touch-callout: none) {
    body {
        -webkit-overflow-scrolling: touch;
    }
    
    .container {
        -webkit-transform: translateZ(0);
        transform: translateZ(0);
    }
}

/* إصلاحات لمتصفحات Android */
@media screen and (-webkit-min-device-pixel-ratio: 1) {
    body {
        -webkit-text-size-adjust: 100%;
    }
}

/* إصلاح مشكلة الزوم في iOS */
@media screen and (max-device-width: 768px) {
    select,
    textarea,
    input[type="text"],
    input[type="password"],
    input[type="datetime"],
    input[type="datetime-local"],
    input[type="date"],
    input[type="month"],
    input[type="time"],
    input[type="week"],
    input[type="number"],
    input[type="email"],
    input[type="url"],
    input[type="search"],
    input[type="tel"],
    input[type="color"] {
        font-size: 16px !important;
    }
}

/* إصلاح مشكلة التمرير الأفقي */
.no-scroll-x {
    overflow-x: hidden !important;
    max-width: 100vw !important;
}

/* تطبيق الإصلاح على العناصر الرئيسية */
html,
body,
.container,
.main-content,
#main-content {
    overflow-x: hidden !important;
    max-width: 100vw !important;
}
