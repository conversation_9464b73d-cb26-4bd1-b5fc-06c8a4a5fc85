/*
 * ملف التصميم لصفحة عرض المودات - محسن للهواتف
 * CSS Styles for Mod Details Page - Mobile Optimized
 * Cloudflare Pages Version - READY FOR DEPLOYMENT
 */

/* إصلاحات أساسية للتجاوب */
* {
    box-sizing: border-box;
}

html {
    width: 100%;
    height: 100%;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    text-size-adjust: 100%;
}

/* إصلاح مشاكل الفيض */
html, body {
    overflow-x: hidden;
    max-width: 100vw;
}

:root {
    --bg-color: #1a1a1a;
    --header-color: #FFA500;
    --text-color: #ffffff;
    --button-color: #FFA500;
    --border-color: #AAAAAA;
    --accent-color: #FFD700;
    --card-color: #2D2D2D;
    --shadow-color: rgba(0,0,0,0.3);
    --font-family: 'Press Start 2P', monospace;
    --border-radius: 8px;
}

body {
    font-family: var(--font-family);
    background-color: var(--bg-color);
    color: var(--text-color);
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    line-height: 1.6;
    /* إصلاحات مهمة للتجاوب */
    width: 100%;
    min-height: 100vh;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    text-size-adjust: 100%;
}

/* Header */
header {
    background: linear-gradient(135deg, var(--header-color) 0%, var(--accent-color) 100%);
    box-shadow: 0 2px 10px var(--shadow-color);
    position: relative;
    padding: 1.5rem 1rem;
    color: var(--text-color);
    min-height: 80px;
    width: 100%;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
}

header h1, .header-content .font-bold {
    margin: 0;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    letter-spacing: 1px;
    text-align: center;
    font-size: 1.5rem;
    flex: 1;
    font-family: var(--font-family);
}

.logo-container {
    width: 60px;
    height: 60px;
}

.channel-logo {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.channel-logo:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

.spacer {
    width: 60px;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    width: 100%;
    /* إصلاحات مهمة للتجاوب */
    box-sizing: border-box;
}

/* تحديث للحاوي مع Tailwind */
.container.mx-auto {
    max-width: 1200px;
    width: 100%;
    box-sizing: border-box;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-color);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-content {
    text-align: center;
    color: var(--text-color);
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 165, 0, 0.3);
    border-top: 4px solid var(--button-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error Screen */
.error-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-color);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9998;
}

.error-content {
    text-align: center;
    color: var(--text-color);
    padding: 40px;
    background: var(--card-color);
    border-radius: var(--border-radius);
    box-shadow: 0 10px 30px var(--shadow-color);
}

.error-icon {
    font-size: 4rem;
    margin-bottom: 20px;
}

/* Preview Notice */
.preview-notice {
    background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
    color: white;
    padding: 15px;
    text-align: center;
    margin-bottom: 20px;
    border-radius: var(--border-radius);
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
}

.preview-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.preview-icon {
    font-size: 1.5rem;
}

/* Mod Info Card */
.mod-info-card {
    background: var(--card-color);
    padding: 30px;
    border-radius: var(--border-radius);
    margin-bottom: 30px;
    box-shadow: 0 8px 25px var(--shadow-color);
    border: 1px solid var(--border-color);
}

.mod-title {
    font-size: 2rem;
    margin: 0 0 20px 0;
    color: var(--accent-color);
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    text-align: center;
}

.mod-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: rgba(255, 165, 0, 0.1);
    border-radius: 5px;
    border-left: 4px solid var(--button-color);
}

.detail-label {
    font-weight: bold;
    color: var(--accent-color);
}

.detail-value {
    color: var(--text-color);
}

/* Image Gallery */
.image-gallery {
    margin-bottom: 30px;
}

.main-image-container {
    position: relative;
    margin-bottom: 20px;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 10px 30px var(--shadow-color);
}

.main-mod-image {
    width: 100%;
    height: auto;
    max-height: 500px;
    object-fit: cover;
    display: block;
    transition: all 0.3s ease;
}

.main-mod-image.fade-in {
    opacity: 0.7;
}

/* Navigation Buttons - نمط البكسل */
.nav-button {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: var(--button-color);
    color: white;
    border: 2px solid var(--accent-color);
    width: 60px;
    height: 60px;
    border-radius: 8px;
    font-size: 1.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    font-family: var(--font-family);
    box-shadow: inset -2px -2px 0 0 #C67C00,
               inset 2px 2px 0 0 #FFD180,
               0 0 10px rgba(255, 165, 0, 0.3);
}

.nav-button:hover {
    background: var(--accent-color);
    transform: translateY(-50%) scale(1.05);
    box-shadow: inset -2px -2px 0 0 #B8860B,
               inset 2px 2px 0 0 #FFFF99,
               0 0 15px rgba(255, 215, 0, 0.5);
}

.prev-button {
    left: 20px;
}

.next-button {
    right: 20px;
}

/* Thumbnails */
.thumbnail-container {
    display: flex;
    gap: 12px;
    overflow-x: auto;
    padding: 15px 0;
    scrollbar-width: thin;
    scrollbar-color: var(--button-color) transparent;
    background: transparent;
}

.thumbnail {
    width: 80px;
    height: 45px;
    object-fit: cover;
    border-radius: 6px;
    cursor: pointer;
    transition: transform 0.2s ease, border-color 0.2s ease;
    border: 2px solid var(--border-color);
    flex-shrink: 0;
    background: var(--card-color);
    will-change: transform;
}

.thumbnail:hover {
    transform: scale(1.08);
    border-color: var(--button-color);
    box-shadow: 0 4px 12px rgba(255, 165, 0, 0.3);
}

.thumbnail.active {
    border-color: var(--accent-color);
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.8),
               inset 0 0 10px rgba(255, 215, 0, 0.3);
    transform: scale(1.1);
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1));
}

/* Description Card */
.mod-description-card {
    background: var(--card-color);
    padding: 30px;
    border-radius: var(--border-radius);
    margin-bottom: 30px;
    box-shadow: 0 8px 25px var(--shadow-color);
    border: 1px solid var(--border-color);
}

.mod-description-card h2 {
    color: var(--accent-color);
    margin-top: 0;
    margin-bottom: 20px;
    font-size: 1.5rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.mod-description-card p {
    line-height: 1.8;
    font-size: 1rem;
    color: var(--text-color);
}

/* Download Section */
.download-section {
    text-align: center;
    margin-bottom: 30px;
}

.download-button, .pixel-button {
    background: linear-gradient(135deg, var(--button-color) 0%, #FF8C00 100%);
    color: white;
    border: none;
    padding: 18px 35px;
    border-radius: var(--border-radius);
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(255, 165, 0, 0.3);
    position: relative;
    overflow: hidden;
    min-width: 280px;
    max-width: 90%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin: 0 auto;
}

/* تحديث للزر الجديد */
.pixel-button {
    image-rendering: pixelated;
    background-color: var(--button-color);
    border: 2px solid var(--accent-color);
    box-shadow: inset -4px -4px 0 0 #C67C00,
               inset 4px 4px 0 0 #FFD180,
               0 0 10px var(--accent-color),
               0 0 20px var(--accent-color);
    animation: pulse-animation 1.5s infinite;
    font-family: var(--font-family);
}

.download-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(255, 165, 0, 0.4);
}

.download-button:active {
    transform: translateY(-1px);
}

.download-button.downloading {
    background: linear-gradient(135deg, #666 0%, #555 100%);
    cursor: not-allowed;
}

.download-button.downloaded {
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
}

.download-icon {
    font-size: 1.2rem;
}

.download-text {
    font-family: 'Roboto', sans-serif;
}

.progress-bar {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 4px;
    background: var(--accent-color);
    width: 0%;
    transition: width 0.3s ease;
}

/* Ad Overlay */
.ad-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.ad-content {
    background: var(--card-color);
    padding: 40px;
    border-radius: var(--border-radius);
    text-align: center;
    max-width: 400px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.ad-header h3 {
    color: var(--accent-color);
    margin-top: 0;
}

.close-ad-btn {
    background: var(--button-color);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    margin-top: 20px;
    transition: all 0.3s ease;
}

.close-ad-btn:hover {
    background: #FF8C00;
    transform: translateY(-2px);
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* تحسينات للأخطاء وإعادة المحاولة */
.retry-button {
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    cursor: pointer;
    font-family: inherit;
    font-size: 14px;
    margin-top: 15px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.retry-button:hover {
    background: linear-gradient(135deg, #45a049 0%, #4CAF50 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.3);
}

.retry-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* تحسينات للإشعارات */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    font-weight: bold;
    z-index: 10000;
    max-width: 300px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    transition: all 0.3s ease;
    font-family: 'Roboto', sans-serif;
    font-size: 14px;
}

.notification.success {
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
}

.notification.error {
    background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
}

.notification.warning {
    background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
}

.notification.info {
    background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
}

.notification-enter {
    opacity: 0;
    transform: translateX(100%);
    animation: slideIn 0.3s ease forwards;
}

.notification-exit {
    animation: slideOut 0.3s ease forwards;
}

@keyframes slideIn {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideOut {
    to {
        opacity: 0;
        transform: translateX(100%);
    }
}

/* تحسينات للتحميل */
.spinner {
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid var(--button-color, #FFA500);
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
    display: inline-block;
}

/* تحسينات للهواتف */
@media (max-width: 768px) {
    .container {
        padding: 15px;
        margin: 0;
        width: 100%;
    }

    .mod-title {
        font-size: 1.5rem;
    }

    .mod-info-card,
    .mod-description-card {
        padding: 20px;
    }

    .mod-details {
        grid-template-columns: 1fr;
    }

    /* إصلاح شبكة المعلومات للهواتف */
    .grid.grid-cols-2 {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 0.5rem;
        width: 100%;
    }

    .grid.grid-cols-2 > div {
        min-width: 0;
        width: 100%;
    }

    .download-button {
        width: 100%;
        padding: 18px;
        font-size: 1.1rem;
    }

    .nav-button {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }

    .prev-button {
        left: 10px;
    }

    .next-button {
        right: 10px;
    }

    .notification {
        right: 10px;
        left: 10px;
        max-width: none;
        font-size: 13px;
    }

    .retry-button {
        width: 100%;
        padding: 15px;
        font-size: 16px;
    }

    .header-content {
        padding: 0 10px;
    }

    header h1 {
        font-size: 1.2rem;
    }

    .logo-container {
        width: 50px;
        height: 50px;
    }

    .spacer {
        width: 50px;
    }
}

/* تحسينات للشاشات الصغيرة جداً */
@media (max-width: 480px) {
    .container {
        padding: 10px;
        margin: 0;
    }

    .mod-title {
        font-size: 1.2rem;
    }

    .mod-info-card,
    .mod-description-card {
        padding: 15px;
    }

    /* تحسين الشبكة للشاشات الصغيرة */
    .grid.grid-cols-2 {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .download-button {
        min-width: auto;
        padding: 15px 20px;
        width: 100%;
    }

    .thumbnail {
        width: 80px;
        height: 50px;
    }

    header h1 {
        font-size: 1rem;
    }

    /* تحسين النصوص للشاشات الصغيرة */
    .info-label {
        font-size: 12px;
    }

    .mod-info {
        font-size: 16px;
    }
}

/* تحسينات للوضع المظلم */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-color: #0a0a0a;
        --card-color: #1a1a1a;
        --border-color: #404040;
    }
}

/* تحسينات للحركة المخفضة */
@media (prefers-reduced-motion: reduce) {
    .notification,
    .retry-button,
    .spinner,
    .main-mod-image,
    .thumbnail,
    .download-button,
    .nav-button {
        animation: none;
        transition: none;
    }

    .notification-enter,
    .notification-exit {
        animation: none;
    }

    .fade-in {
        animation: none;
    }
}

/* تحسينات للطباعة */
@media print {
    .nav-button,
    .download-button,
    .ad-overlay,
    .loading-screen,
    .error-screen {
        display: none !important;
    }

    body {
        background: white;
        color: black;
    }

    .mod-info-card,
    .mod-description-card {
        background: white;
        border: 1px solid #ccc;
        box-shadow: none;
    }
}

/* ستايلات مستوحاة من صفحة المود */
.pixel-border {
    box-shadow: inset 0 0 0 1px var(--border-color);
    border: 1px solid var(--border-color);
}

.mod-header {
    background-color: var(--card-color);
    color: var(--text-color);
    padding: 15px;
    text-align: center;
}

.mod-container {
    background-color: var(--card-color);
}

@keyframes pulse-animation {
    0% {
        transform: scale(1);
        box-shadow: inset -4px -4px 0 0 #C67C00, inset 4px 4px 0 0 #FFD180,
                   0 0 10px var(--accent-color), 0 0 20px var(--accent-color);
    }
    50% {
        transform: scale(1.05);
        box-shadow: inset -4px -4px 0 0 #C67C00, inset 4px 4px 0 0 #FFD180,
                   0 0 15px var(--accent-color), 0 0 30px var(--accent-color);
    }
    100% {
        transform: scale(1);
        box-shadow: inset -4px -4px 0 0 #C67C00, inset 4px 4px 0 0 #FFD180,
                   0 0 10px var(--accent-color), 0 0 20px var(--accent-color);
    }
}

.info-label {
    color: var(--button-color);
    font-size: 14px;
}

.mod-info {
    font-size: 18px;
    color: var(--text-color);
    letter-spacing: -1px;
}

.download-instructions {
    background-color: var(--bg-color);
    padding: 15px;
    border: 1px solid var(--button-color);
    margin: 20px 0;
    font-size: 14px;
    line-height: 1.5;
    border-radius: var(--border-radius);
}

/* تحسينات الصور الجديدة */
.main-image-section {
    margin-bottom: 1.5rem;
}

.additional-images-section {
    margin-bottom: 1.5rem;
}

/* إزالة اللون الأصفر من الصورة الرئيسية */
.main-image-glow {
    position: relative;
    overflow: hidden;
    background-color: transparent;
    border-radius: var(--border-radius);
}

.main-image-glow .particle {
    position: absolute;
    width: 6px;
    height: 6px;
    background-color: var(--accent-color);
    box-shadow: 0 0 3px var(--accent-color), 0 0 2px var(--button-color);
    opacity: 0;
    animation: emanate-squares-from-bottom 3s infinite ease-out;
}

@keyframes emanate-squares-from-bottom {
    0% { opacity: 0.9; transform: translate(0, 0) scale(0.5) rotate(0deg); }
    20% { opacity: 1; transform: translate(var(--tx, 0px), var(--ty, 0px)) scale(1) rotate(45deg); }
    80% { opacity: 0.5; transform: translate(calc(var(--tx, 0px) * 2.5), calc(var(--ty, 0px) * 2.5)) scale(0.8) rotate(90deg); }
    100% { opacity: 0; transform: translate(calc(var(--tx, 0px) * 3), calc(var(--ty, 0px) * 3)) scale(0) rotate(135deg); }
}

/* حاوي الصور المصغرة المحسن - بدون ألوان */
.thumbnail-container-centered {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 12px;
    overflow-x: auto;
    padding: 15px 0;
    scrollbar-width: thin;
    scrollbar-color: transparent transparent;
    background: transparent;
    border-radius: 0;
    /* تحسينات الأداء */
    contain: layout style;
    will-change: scroll-position;
}

/* إزالة الخلفية البرتقالية من الصور المصغرة */
.thumbnail-container-centered::-webkit-scrollbar {
    height: 6px;
}

.thumbnail-container-centered::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 3px;
}

.thumbnail-container-centered::-webkit-scrollbar-thumb {
    background: var(--button-color);
    border-radius: 3px;
}

.thumbnail-container-centered::-webkit-scrollbar-thumb:hover {
    background: var(--accent-color);
}

/* تحسينات أداء عامة */
.main-mod-image {
    will-change: auto;
    contain: layout;
}

/* تحسين الانتقالات */
.thumbnail, .main-mod-image, .nav-button {
    backface-visibility: hidden;
    perspective: 1000px;
}

/* إزالة التكرار والتأثيرات المزعجة */

@keyframes emanate-squares-from-bottom {
    0% { opacity: 0.9; transform: translate(0, 0) scale(0.5) rotate(0deg); }
    20% { opacity: 1; transform: translate(var(--tx, 0px), var(--ty, 0px)) scale(1) rotate(45deg); }
    80% { opacity: 0.5; transform: translate(calc(var(--tx, 0px) * 2.5), calc(var(--ty, 0px) * 2.5)) scale(0.8) rotate(90deg); }
    100% { opacity: 0; transform: translate(calc(var(--tx, 0px) * 3), calc(var(--ty, 0px) * 3)) scale(0) rotate(135deg); }
}

/* تحسين الذاكرة */
.thumbnail {
    image-rendering: optimizeQuality;
    image-rendering: -webkit-optimize-contrast;
}

/* تحسين التمرير */
.container {
    scroll-behavior: smooth;
}

/* تقليل إعادة الرسم */
.pixel-button {
    contain: layout style;
}

/* إصلاحات Tailwind CSS للتجاوب */
@media (max-width: 768px) {
    .grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
    }

    .gap-4 {
        gap: 1rem !important;
    }

    .p-4 {
        padding: 1rem !important;
    }

    .mb-4 {
        margin-bottom: 1rem !important;
    }

    .mb-6 {
        margin-bottom: 1.5rem !important;
    }
}

@media (max-width: 480px) {
    .grid-cols-2 {
        grid-template-columns: repeat(1, minmax(0, 1fr)) !important;
    }

    .gap-4 {
        gap: 0.75rem !important;
    }

    .p-4 {
        padding: 0.75rem !important;
    }
}

/* تحسينات إضافية للاستجابة */
@media (max-width: 480px) {
    .container {
        padding: 10px;
    }

    .mod-title {
        font-size: 1.2rem;
        line-height: 1.4;
    }

    .main-mod-image {
        min-height: 200px;
    }

    .thumbnail {
        width: 70px;
        height: 45px;
    }

    .download-button {
        padding: 15px 25px;
        font-size: 1rem;
        min-width: 200px;
    }

    .nav-button {
        width: 35px;
        height: 35px;
        font-size: 18px;
    }
}

/* تحسينات للأجهزة اللوحية */
@media (min-width: 481px) and (max-width: 768px) {
    .container {
        padding: 15px;
    }

    .thumbnail {
        width: 80px;
        height: 50px;
    }
}

/* ضمان عدم كسر التخطيط */
* {
    box-sizing: border-box;
}

img {
    max-width: 100%;
    height: auto;
    display: block;
    /* إصلاحات مهمة للصور */
    width: 100%;
    object-fit: cover;
}

/* إصلاحات خاصة للصورة الرئيسية */
#main-mod-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

/* إصلاح مشاكل الفيض */
.main-image-container,
.thumbnail-container {
    overflow: hidden;
}

/* إصلاحات شاملة لمنع تجاوز حدود الشاشة */
.mod-container,
.pixel-border,
.download-instructions,
.main-image-section,
.additional-images-section {
    max-width: 100%;
    box-sizing: border-box;
    overflow: hidden;
}

/* إصلاح خاص للأزرار الثابتة */
.fixed.bottom-0 {
    left: 0;
    right: 0;
    width: 100%;
    box-sizing: border-box;
    padding: 1rem;
}

/* إصلاح للنصوص الطويلة */
.mod-info,
.info-label,
.download-instructions {
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
}
