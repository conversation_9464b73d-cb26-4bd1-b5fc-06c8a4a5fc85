services:
  - type: web
    name: minecraft-mods-bot
    env: python
    plan: free
    buildCommand: pip install -r requirements.txt
    startCommand: python main.py
    envVars:
      - key: PYTHON_VERSION
        value: 3.11.0
      - key: BOT_TOKEN
        value: your_bot_token_here
      - key: ADMIN_CHAT_ID
        value: your_admin_id_here
      - key: SUPABASE_URL
        value: your_supabase_url
      - key: SUPABASE_KEY
        value: your_supabase_key
      - key: WEB_SERVER_URL
        value: https://1c547fe5.sendaddons.pages.dev
      - key: WEB_SERVER_PORT
        value: 5000
      - key: TELEGRAM_WEB_APP_PORT
        value: 5001
      - key: ENVIRONMENT
        value: production
      - key: DEBUG
        value: false
      - key: LOG_LEVEL
        value: INFO
