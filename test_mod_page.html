<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="format-detection" content="telephone=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <title>اختبار صفحة المود - Test Mod Page</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Press+Start+2P&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="cloudflare_ready/style.css">
    <link rel="stylesheet" href="cloudflare_ready/responsive-fixes.css">
    <link rel="stylesheet" href="cloudflare_ready/mobile-fix.css">
    <style>
        body {
            font-family: 'Press Start 2P', cursive;
            background-color: #1a1a1a;
            color: white;
        }
        .header-bg {
            background-color: #FFA500;
        }
        .test-section {
            background-color: #2D2D2D;
            border: 1px solid #AAAAAA;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-size: 12px;
        }
        .test-pass {
            background-color: #4CAF50;
            color: white;
        }
        .test-fail {
            background-color: #f44336;
            color: white;
        }
        .test-warning {
            background-color: #ff9800;
            color: white;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header-bg text-white p-4 flex justify-center items-center">
        <div class="font-bold text-2xl">اختبار صفحة المود - Test Page</div>
    </header>

    <div class="container mx-auto p-4 pb-24">
        <!-- Test Results Section -->
        <div class="test-section">
            <h2 class="text-xl mb-4">نتائج الاختبار - Test Results</h2>
            <div id="test-results"></div>
            <button onclick="runAllTests()" class="pixel-button mt-4">تشغيل جميع الاختبارات - Run All Tests</button>
        </div>

        <!-- Mod Title -->
        <div class="mod-header pixel-border mb-4">
            <h1 class="text-2xl mod-title">مود تجريبي للاختبار - Test Demo Mod</h1>
        </div>

        <!-- Main Mod Image Display -->
        <div class="mod-container mb-4 p-1 image-glow-effect">
            <div class="relative w-full bg-gray-700" style="padding-top: 56.25%;">
                <img id="main-mod-image" class="absolute inset-0 w-full h-full object-cover"
                     src="https://picsum.photos/800/450?random=1"
                     alt="صورة المود">
            </div>
        </div>

        <!-- Thumbnail Navigation and Controls -->
        <div class="flex items-center justify-center mb-4">
            <button id="prev-image" class="nav-button pixel-border mr-2"><</button>
            <div id="thumbnail-container" class="flex-grow">
                <!-- Thumbnails will be inserted here by JavaScript -->
            </div>
            <button id="next-image" class="nav-button pixel-border ml-2">></button>
        </div>

        <!-- Mod Info - Testing Grid Layout -->
        <div class="grid grid-cols-2 gap-4 mb-6">
            <div class="mod-container pixel-border p-4">
                <p class="info-label version-label">الإصدار - Version</p>
                <p class="mod-info mod-version">1.0.0</p>
            </div>
            <div class="mod-container pixel-border p-4">
                <p class="info-label loader-label">النوع - Category</p>
                <p class="mod-info mod-category">Addons</p>
            </div>
        </div>

        <!-- Mod Description -->
        <div class="mod-container pixel-border p-4 mb-6">
            <p class="info-label text-center description-label">الوصف - Description</p>
            <p class="mod-info mt-2 text-center mod-description">
                هذا مود تجريبي لاختبار جميع الميزات والتأكد من عملها بشكل صحيح على الهواتف والحاسوب.
                This is a test mod to verify all features work correctly on mobile and desktop.
            </p>
        </div>

        <!-- Download Button -->
        <div class="fixed bottom-0 left-0 right-0 p-4 z-50 flex justify-center">
            <button id="download-button" class="pixel-button text-xl"
                    style="position: relative; overflow: hidden; padding: 15px 12px; min-width: 160px; min-height: 55px;" onclick="testDownload()">
                <div class="progress-bar" id="progress-bar" style="position: absolute; bottom: 3px; right: 8px; height: 2px; background-color: #4CAF50; width: 0%; max-width: calc(100% - 16px); transition: width 0.3s ease; z-index: 1; border-radius: 2px;"></div>
                <span class="download-icon" id="download-icon">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor" style="image-rendering: pixelated;">
                        <path d="M3 3h2v2H3V3zm4 0h2v2H7V3zm4 0h2v2h-2V3zm4 0h2v2h-2V3zm4 0h2v2h-2V3zM3 7h2v2H3V7zm16 0h2v2h-2V7zM3 11h2v2H3v-2zm4 0h2v2H7v-2zm4 0h2v2h-2v-2zm4 0h2v2h-2v-2zm4 0h2v2h-2v-2zM3 15h2v2H3v-2zm16 0h2v2h-2v-2zM3 19h2v2H3v-2zm4 0h2v2H7v-2zm4 0h2v2h-2v-2zm4 0h2v2h-2v-2zm4 0h2v2h-2v-2z"/>
                    </svg>
                </span>
                <span id="download-text">تحميل المود - Download Mod</span>
            </button>
        </div>
    </div>

    <script>
        // Test data - استخدام صور صالحة
        const testImageUrls = [
            'https://picsum.photos/800/450?random=1',
            'https://picsum.photos/800/450?random=2',
            'https://picsum.photos/800/450?random=3'
        ];
        
        let currentImageIndex = 0;
        const mainModImage = document.getElementById('main-mod-image');
        const prevButton = document.getElementById('prev-image');
        const nextButton = document.getElementById('next-image');
        const thumbnailContainer = document.getElementById('thumbnail-container');

        // Test functions
        function runAllTests() {
            const results = document.getElementById('test-results');
            results.innerHTML = '';
            
            const tests = [
                testResponsiveLayout,
                testImageGallery,
                testProgressBar,
                testGridLayout,
                testDownloadButton,
                testMobileOptimization
            ];
            
            tests.forEach(test => {
                try {
                    const result = test();
                    addTestResult(result.name, result.status, result.message);
                } catch (error) {
                    addTestResult(test.name, 'fail', `خطأ في الاختبار: ${error.message}`);
                }
            });
        }

        function addTestResult(name, status, message) {
            const results = document.getElementById('test-results');
            const div = document.createElement('div');
            div.className = `test-result test-${status}`;
            div.innerHTML = `<strong>${name}:</strong> ${message}`;
            results.appendChild(div);
        }

        function testResponsiveLayout() {
            const container = document.querySelector('.container');
            const isResponsive = container && window.getComputedStyle(container).width === '100%';
            
            return {
                name: 'اختبار التجاوب - Responsive Layout',
                status: isResponsive ? 'pass' : 'fail',
                message: isResponsive ? 'التخطيط متجاوب بشكل صحيح' : 'مشكلة في التجاوب'
            };
        }

        function testImageGallery() {
            const hasMainImage = mainModImage && mainModImage.src;
            const hasNavButtons = prevButton && nextButton;
            
            return {
                name: 'اختبار معرض الصور - Image Gallery',
                status: hasMainImage && hasNavButtons ? 'pass' : 'fail',
                message: hasMainImage && hasNavButtons ? 'معرض الصور يعمل بشكل صحيح' : 'مشكلة في معرض الصور'
            };
        }

        function testProgressBar() {
            const progressBar = document.getElementById('progress-bar');
            const hasCorrectPosition = progressBar && 
                window.getComputedStyle(progressBar).position === 'absolute' &&
                window.getComputedStyle(progressBar).right !== 'auto';
            
            return {
                name: 'اختبار شريط التقدم - Progress Bar',
                status: hasCorrectPosition ? 'pass' : 'fail',
                message: hasCorrectPosition ? 'شريط التقدم في الموضع الصحيح' : 'مشكلة في موضع شريط التقدم'
            };
        }

        function testGridLayout() {
            const grid = document.querySelector('.grid.grid-cols-2');
            const gridStyle = window.getComputedStyle(grid);
            const isGrid = gridStyle.display === 'grid';
            const hasTwoColumns = gridStyle.gridTemplateColumns.includes('1fr 1fr') || 
                                 gridStyle.gridTemplateColumns.includes('minmax');
            
            return {
                name: 'اختبار تخطيط الشبكة - Grid Layout',
                status: isGrid && hasTwoColumns ? 'pass' : 'fail',
                message: isGrid && hasTwoColumns ? 'تخطيط الشبكة صحيح (أفقي)' : 'مشكلة في تخطيط الشبكة'
            };
        }

        function testDownloadButton() {
            const downloadButton = document.getElementById('download-button');
            const isFixed = downloadButton && 
                window.getComputedStyle(downloadButton.parentElement).position === 'fixed';
            
            return {
                name: 'اختبار زر التحميل - Download Button',
                status: isFixed ? 'pass' : 'fail',
                message: isFixed ? 'زر التحميل في الموضع الصحيح' : 'مشكلة في موضع زر التحميل'
            };
        }

        function testMobileOptimization() {
            const isMobile = window.innerWidth <= 768;
            const container = document.querySelector('.container');
            const containerPadding = window.getComputedStyle(container).padding;
            
            return {
                name: 'اختبار تحسين الهواتف - Mobile Optimization',
                status: 'pass',
                message: isMobile ? 
                    `تم اكتشاف جهاز محمول - عرض الشاشة: ${window.innerWidth}px` : 
                    `جهاز سطح مكتب - عرض الشاشة: ${window.innerWidth}px`
            };
        }

        // Initialize image gallery
        function initializeGallery() {
            updateThumbnails();
            
            prevButton.addEventListener('click', () => {
                currentImageIndex = (currentImageIndex - 1 + testImageUrls.length) % testImageUrls.length;
                updateImage();
            });
            
            nextButton.addEventListener('click', () => {
                currentImageIndex = (currentImageIndex + 1) % testImageUrls.length;
                updateImage();
            });
        }

        function updateImage() {
            if (testImageUrls.length > 0 && mainModImage) {
                mainModImage.src = testImageUrls[currentImageIndex];
                updateThumbnails();
            }
        }

        function updateThumbnails() {
            thumbnailContainer.innerHTML = '';
            
            const thumbnailsWrapper = document.createElement('div');
            thumbnailsWrapper.className = 'thumbnails-wrapper flex justify-center gap-3';
            
            testImageUrls.forEach((url, index) => {
                const thumbnail = document.createElement('img');
                thumbnail.src = url;
                thumbnail.className = 'thumbnail w-20 h-12 object-cover cursor-pointer border-2 border-gray-400 rounded';
                thumbnail.alt = `صورة ${index + 1}`;
                
                if (index === currentImageIndex) {
                    thumbnail.classList.add('active', 'border-yellow-400');
                }
                
                thumbnail.addEventListener('click', () => {
                    currentImageIndex = index;
                    updateImage();
                });
                
                thumbnailsWrapper.appendChild(thumbnail);
            });
            
            thumbnailContainer.appendChild(thumbnailsWrapper);
        }

        function testDownload() {
            const button = document.getElementById('download-button');
            const progressBar = document.getElementById('progress-bar');
            const downloadText = document.getElementById('download-text');
            
            // Simulate download
            downloadText.textContent = 'جاري التحميل... - Downloading...';
            button.classList.add('downloading');
            
            let progress = 0;
            const interval = setInterval(() => {
                progress += 10;
                progressBar.style.width = progress + '%';
                
                if (progress >= 100) {
                    clearInterval(interval);
                    downloadText.textContent = 'تم التحميل - Downloaded ✓';
                    button.classList.remove('downloading');
                    button.classList.add('downloaded');
                    
                    setTimeout(() => {
                        // Reset
                        downloadText.textContent = 'تحميل المود - Download Mod';
                        button.classList.remove('downloaded');
                        progressBar.style.width = '0%';
                    }, 3000);
                }
            }, 200);
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', () => {
            initializeGallery();
            
            // Auto-run tests after a short delay
            setTimeout(() => {
                runAllTests();
            }, 1000);
        });
    </script>
</body>
</html>
